import { db } from '@/lib/db/schema';
import { assets, tags, assetTags } from '@/lib/db/schema';
import { sql, desc, and, or, like, ilike, inArray } from 'drizzle-orm';
import { Asset } from '@/lib/storage/indexingService';
import { PerformanceMonitor } from './performanceMonitor';

/**
 * Optimized Search Service with:
 * 1. Full-text search using PostgreSQL
 * 2. Boolean operators (AND, OR, NOT)
 * 3. Phrase matching with quotes
 * 4. Tag-based search
 * 5. Metadata search
 * 6. Performance monitoring
 */
export class SearchService {
  private static instance: SearchService;
  private performanceMonitor = PerformanceMonitor.getInstance();

  private constructor() {
    console.log('🔍 SearchService initialized with advanced search capabilities');
  }

  static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  /**
   * Advanced search with multiple strategies
   */
  async search(query: string, options: {
    limit?: number;
    offset?: number;
    type?: string;
    sortBy?: 'relevance' | 'date' | 'name' | 'size';
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{
    assets: Asset[];
    total: number;
    searchTime: number;
    strategy: string;
  }> {
    const startTime = Date.now();
    const operationId = `search-${Date.now()}`;
    
    this.performanceMonitor.startTimer(operationId);
    
    try {
      const {
        limit = 100,
        offset = 0,
        type,
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = options;

      console.log(`🔍 Advanced search: "${query}" (${sortBy} ${sortOrder})`);

      // Parse search query
      const searchTerms = this.parseSearchQuery(query);
      console.log('📝 Parsed search terms:', searchTerms);

      let results: Asset[] = [];
      let strategy = '';

      // Choose search strategy based on query complexity
      if (searchTerms.phrases.length > 0) {
        // Phrase search
        results = await this.phraseSearch(searchTerms, { limit, offset, type, sortBy, sortOrder });
        strategy = 'phrase';
      } else if (searchTerms.tags.length > 0) {
        // Tag-based search
        results = await this.tagSearch(searchTerms, { limit, offset, type, sortBy, sortOrder });
        strategy = 'tag';
      } else if (searchTerms.exclusions.length > 0 || searchTerms.inclusions.length > 1) {
        // Boolean search
        results = await this.booleanSearch(searchTerms, { limit, offset, type, sortBy, sortOrder });
        strategy = 'boolean';
      } else {
        // Full-text search
        results = await this.fullTextSearch(searchTerms, { limit, offset, type, sortBy, sortOrder });
        strategy = 'fulltext';
      }

      const searchTime = this.performanceMonitor.endTimer(
        operationId,
        `Search: ${query}`,
        'search',
        { query, strategy, resultCount: results.length }
      );

      console.log(`✅ Search completed in ${searchTime}ms using ${strategy} strategy (${results.length} results)`);

      return {
        assets: results,
        total: results.length, // For now, return actual count
        searchTime,
        strategy
      };

    } catch (error) {
      this.performanceMonitor.endTimer(operationId, `Search Error: ${query}`, 'search', { error: true });
      console.error('❌ Search error:', error);
      throw error;
    }
  }

  /**
   * Parse search query into components
   */
  private parseSearchQuery(query: string): {
    inclusions: string[];
    exclusions: string[];
    phrases: string[];
    tags: string[];
    raw: string;
  } {
    const result = {
      inclusions: [] as string[],
      exclusions: [] as string[],
      phrases: [] as string[],
      tags: [] as string[],
      raw: query
    };

    // Extract quoted phrases
    const phraseRegex = /"([^"]+)"/g;
    let match;
    while ((match = phraseRegex.exec(query)) !== null) {
      result.phrases.push(match[1]);
      query = query.replace(match[0], '');
    }

    // Extract tags (words starting with #)
    const tagRegex = /#(\w+)/g;
    while ((match = tagRegex.exec(query)) !== null) {
      result.tags.push(match[1]);
      query = query.replace(match[0], '');
    }

    // Extract exclusions (words starting with -)
    const exclusionRegex = /-(\w+)/g;
    while ((match = exclusionRegex.exec(query)) !== null) {
      result.exclusions.push(match[1]);
      query = query.replace(match[0], '');
    }

    // Remaining words are inclusions
    const words = query.trim().split(/\s+/).filter(word => word.length > 0);
    result.inclusions.push(...words);

    return result;
  }

  /**
   * Full-text search using PostgreSQL
   */
  private async fullTextSearch(
    searchTerms: ReturnType<typeof this.parseSearchQuery>,
    options: any
  ): Promise<Asset[]> {
    const { limit, offset, type, sortBy, sortOrder } = options;
    
    if (searchTerms.inclusions.length === 0) {
      return [];
    }

    const searchQuery = searchTerms.inclusions.join(' & ');
    
    let query = db.select().from(assets);
    const conditions = [];

    // Full-text search condition
    conditions.push(
      sql`to_tsvector('english', filename || ' ' || COALESCE(metadata->>'description', '')) @@ to_tsquery('english', ${searchQuery})`
    );

    // Type filter
    if (type) {
      conditions.push(sql`type = ${type}`);
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions)) as any;
    }

    // Sorting
    if (sortBy === 'relevance') {
      query = query.orderBy(
        sql`ts_rank(to_tsvector('english', filename), to_tsquery('english', ${searchQuery})) DESC`
      ) as any;
    } else {
      query = this.applySorting(query, sortBy, sortOrder);
    }

    return await query.limit(limit).offset(offset);
  }

  /**
   * Phrase search for exact matches
   */
  private async phraseSearch(
    searchTerms: ReturnType<typeof this.parseSearchQuery>,
    options: any
  ): Promise<Asset[]> {
    const { limit, offset, type } = options;
    
    let query = db.select().from(assets);
    const conditions = [];

    // Search for phrases in filename and metadata
    searchTerms.phrases.forEach(phrase => {
      conditions.push(
        or(
          ilike(assets.filename, `%${phrase}%`),
          sql`metadata::text ILIKE ${`%${phrase}%`}`
        )
      );
    });

    // Type filter
    if (type) {
      conditions.push(sql`type = ${type}`);
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions)) as any;
    }

    return await query
      .orderBy(desc(assets.lastModified))
      .limit(limit)
      .offset(offset);
  }

  /**
   * Tag-based search
   */
  private async tagSearch(
    searchTerms: ReturnType<typeof this.parseSearchQuery>,
    options: any
  ): Promise<Asset[]> {
    const { limit, offset, type } = options;

    // Get tag IDs
    const tagRecords = await db.select({ id: tags.id })
      .from(tags)
      .where(
        or(
          ...searchTerms.tags.map(tag => ilike(tags.name, `%${tag}%`))
        )
      );

    if (tagRecords.length === 0) {
      return [];
    }

    const tagIds = tagRecords.map(t => t.id);

    // Find assets with these tags
    let query = db.select({
      id: assets.id,
      filename: assets.filename,
      filePath: assets.filePath,
      fileUrl: assets.fileUrl,
      thumbnailUrl: assets.thumbnailUrl,
      type: assets.type,
      size: assets.size,
      lastModified: assets.lastModified,
      metadata: assets.metadata,
      createdAt: assets.createdAt,
      updatedAt: assets.updatedAt
    })
    .from(assets)
    .innerJoin(assetTags, sql`${assets.id} = ${assetTags.assetId}`)
    .where(inArray(assetTags.tagId, tagIds));

    // Type filter
    if (type) {
      query = query.where(sql`${assets.type} = ${type}`) as any;
    }

    return await query
      .orderBy(desc(assets.lastModified))
      .limit(limit)
      .offset(offset);
  }

  /**
   * Boolean search with AND/OR/NOT logic
   */
  private async booleanSearch(
    searchTerms: ReturnType<typeof this.parseSearchQuery>,
    options: any
  ): Promise<Asset[]> {
    const { limit, offset, type } = options;
    
    let query = db.select().from(assets);
    const conditions = [];

    // Inclusion conditions (AND logic)
    if (searchTerms.inclusions.length > 0) {
      const inclusionConditions = searchTerms.inclusions.map(term =>
        or(
          ilike(assets.filename, `%${term}%`),
          sql`metadata::text ILIKE ${`%${term}%`}`
        )
      );
      conditions.push(and(...inclusionConditions));
    }

    // Exclusion conditions (NOT logic)
    if (searchTerms.exclusions.length > 0) {
      const exclusionConditions = searchTerms.exclusions.map(term =>
        and(
          sql`${assets.filename} NOT ILIKE ${`%${term}%`}`,
          sql`metadata::text NOT ILIKE ${`%${term}%`}`
        )
      );
      conditions.push(and(...exclusionConditions));
    }

    // Type filter
    if (type) {
      conditions.push(sql`type = ${type}`);
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions)) as any;
    }

    return await query
      .orderBy(desc(assets.lastModified))
      .limit(limit)
      .offset(offset);
  }

  /**
   * Apply sorting to query
   */
  private applySorting(query: any, sortBy: string, sortOrder: string): any {
    const isDesc = sortOrder === 'desc';
    
    switch (sortBy) {
      case 'date':
        return isDesc 
          ? query.orderBy(desc(assets.lastModified))
          : query.orderBy(assets.lastModified);
      case 'name':
        return isDesc
          ? query.orderBy(desc(assets.filename))
          : query.orderBy(assets.filename);
      case 'size':
        return isDesc
          ? query.orderBy(desc(assets.size))
          : query.orderBy(assets.size);
      default:
        return query.orderBy(desc(assets.lastModified));
    }
  }

  /**
   * Get search suggestions based on existing data
   */
  async getSearchSuggestions(partial: string, limit: number = 10): Promise<string[]> {
    if (partial.length < 2) {
      return [];
    }

    try {
      // Get filename suggestions
      const filenameResults = await db.select({ filename: assets.filename })
        .from(assets)
        .where(ilike(assets.filename, `%${partial}%`))
        .limit(limit);

      // Get tag suggestions
      const tagResults = await db.select({ name: tags.name })
        .from(tags)
        .where(ilike(tags.name, `%${partial}%`))
        .limit(limit);

      const suggestions = [
        ...filenameResults.map(r => r.filename),
        ...tagResults.map(r => `#${r.name}`)
      ];

      return [...new Set(suggestions)].slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting search suggestions:', error);
      return [];
    }
  }
}
