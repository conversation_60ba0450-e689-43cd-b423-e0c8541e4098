import bcrypt from 'bcryptjs';
import { db, users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  isActive: boolean;
  lastLogin?: Date;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  avatar?: string;
}

export class AuthService {
  private static instance: AuthService;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Hash a password using bcrypt
   */
  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify a password against a hash
   */
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate a unique user ID
   */
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Check if user already exists
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, userData.email))
        .limit(1);

      if (existingUser.length > 0) {
        throw new Error('User with this email already exists');
      }

      // Hash the password
      const passwordHash = await this.hashPassword(userData.password);

      // Create user record
      const userId = this.generateUserId();
      const [newUser] = await db
        .insert(users)
        .values({
          id: userId,
          email: userData.email,
          passwordHash,
          name: userData.name,
          role: userData.role,
          avatar: userData.avatar,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      // Return user without password hash
      return {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role as 'admin' | 'editor' | 'viewer',
        isActive: newUser.isActive,
        lastLogin: newUser.lastLogin || undefined,
        avatar: newUser.avatar || undefined,
        createdAt: newUser.createdAt,
        updatedAt: newUser.updatedAt,
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Authenticate a user with email and password
   */
  async authenticateUser(email: string, password: string): Promise<User | null> {
    try {
      // Find user by email
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (!user) {
        return null;
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, user.passwordHash);
      if (!isValidPassword) {
        return null;
      }

      // Update last login
      await db
        .update(users)
        .set({ 
          lastLogin: new Date(),
          updatedAt: new Date()
        })
        .where(eq(users.id, user.id));

      // Return user without password hash
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role as 'admin' | 'editor' | 'viewer',
        isActive: user.isActive,
        lastLogin: new Date(),
        avatar: user.avatar || undefined,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      console.error('Error authenticating user:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role as 'admin' | 'editor' | 'viewer',
        isActive: user.isActive,
        lastLogin: user.lastLogin || undefined,
        avatar: user.avatar || undefined,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers(): Promise<User[]> {
    try {
      const allUsers = await db
        .select()
        .from(users)
        .orderBy(users.createdAt);

      return allUsers.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role as 'admin' | 'editor' | 'viewer',
        isActive: user.isActive,
        lastLogin: user.lastLogin || undefined,
        avatar: user.avatar || undefined,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async updateUser(id: string, updates: Partial<CreateUserData>): Promise<User | null> {
    try {
      const updateData: any = {
        updatedAt: new Date(),
      };

      if (updates.email) updateData.email = updates.email;
      if (updates.name) updateData.name = updates.name;
      if (updates.role) updateData.role = updates.role;
      if (updates.avatar !== undefined) updateData.avatar = updates.avatar;
      if (updates.password) {
        updateData.passwordHash = await this.hashPassword(updates.password);
      }

      const [updatedUser] = await db
        .update(users)
        .set(updateData)
        .where(eq(users.id, id))
        .returning();

      if (!updatedUser) {
        return null;
      }

      return {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role as 'admin' | 'editor' | 'viewer',
        isActive: updatedUser.isActive,
        lastLogin: updatedUser.lastLogin || undefined,
        avatar: updatedUser.avatar || undefined,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
      };
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete user (deactivate)
   */
  async deleteUser(id: string): Promise<boolean> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
        .returning();

      return !!updatedUser;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  /**
   * Initialize default users if none exist
   */
  async initializeDefaultUsers(): Promise<void> {
    try {
      const existingUsers = await db.select().from(users).limit(1);
      
      if (existingUsers.length === 0) {
        console.log('🔐 No users found, creating default users...');
        
        // Create admin user
        await this.createUser({
          email: '<EMAIL>',
          password: 'Asharq!@2025',
          name: 'Admin User',
          role: 'admin',
        });

        // Create editor user
        await this.createUser({
          email: '<EMAIL>',
          password: 'Dam2025',
          name: 'Michel Gabriel',
          role: 'editor',
        });

        console.log('✅ Default users created successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing default users:', error);
    }
  }
}
