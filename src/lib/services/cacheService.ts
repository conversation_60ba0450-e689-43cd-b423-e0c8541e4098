/**
 * Cache Service for API responses and data caching
 */
import React from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheOptions {
  ttl?: number; // Default TTL in milliseconds
  maxSize?: number; // Maximum number of entries
}

export class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL: number;
  private maxSize: number;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
    this.maxSize = options.maxSize || 100; // 100 entries max
  }

  /**
   * Get cached data
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set cached data
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  /**
   * Delete cached entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      maxSize: this.maxSize
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  }

  /**
   * Generate cache key from parameters
   */
  static generateKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    
    return `${prefix}:${sortedParams}`;
  }
}

// Global cache instances
export const apiCache = new CacheService({
  ttl: 2 * 60 * 1000, // 2 minutes for API responses
  maxSize: 50
});

export const assetCache = new CacheService({
  ttl: 10 * 60 * 1000, // 10 minutes for asset data
  maxSize: 200
});

export const searchCache = new CacheService({
  ttl: 5 * 60 * 1000, // 5 minutes for search results
  maxSize: 30
});

/**
 * Cached fetch wrapper
 */
export async function cachedFetch<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: { ttl?: number; cache?: CacheService } = {}
): Promise<T> {
  const cache = cacheOptions.cache || apiCache;
  const cacheKey = CacheService.generateKey('fetch', { url, ...options });

  // Try to get from cache first
  const cached = cache.get<T>(cacheKey);
  if (cached) {
    console.log(`🎯 Cache hit for: ${url}`);
    return cached;
  }

  console.log(`🌐 Cache miss, fetching: ${url}`);
  
  // Fetch from network
  const response = await fetch(url, options);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  
  // Cache the response
  cache.set(cacheKey, data, cacheOptions.ttl);
  
  return data;
}

/**
 * React hook for cached API calls
 */
export function useCachedApi<T>(
  url: string | null,
  options: RequestInit = {},
  cacheOptions: { ttl?: number; cache?: CacheService } = {}
) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!url) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await cachedFetch<T>(url, options, cacheOptions);
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [url, JSON.stringify(options), cacheOptions.ttl]);

  return { data, loading, error };
}

// Auto cleanup expired entries every 5 minutes
setInterval(() => {
  const apiRemoved = apiCache.cleanup();
  const assetRemoved = assetCache.cleanup();
  const searchRemoved = searchCache.cleanup();
  
  if (apiRemoved + assetRemoved + searchRemoved > 0) {
    console.log(`🧹 Cache cleanup: removed ${apiRemoved + assetRemoved + searchRemoved} expired entries`);
  }
}, 5 * 60 * 1000);
