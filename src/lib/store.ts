'use client'

import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { Asset, Collection, Tag, User } from "./types";
import { v4 as uuidv4 } from "uuid";

import { debounce } from "./utils/performance";
import { storageService } from "./storage/storageService";

// Helper functions for localStorage interactions
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    // Only access localStorage on client side
    const stored = typeof localStorage !== 'undefined' ? localStorage.getItem(key) : null;
    if (!stored) return defaultValue;
    return JSON.parse(stored) as T;
  } catch (error) {
    console.error(`Error parsing ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setInStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return;
  try {
    // Only access localStorage on client side
    if (typeof localStorage !== 'undefined') {
      // Try to stringify the value first to catch any circular reference errors
      const stringified = JSON.stringify(value);
      
      // Check if the stringified value is too large for localStorage
      // Most browsers have a 5MB limit, but we'll be conservative
      if (stringified.length > 4000000) { // ~4MB
        console.error(`Value for ${key} is too large for localStorage (${(stringified.length/1024/1024).toFixed(2)}MB)`);
        return;
      }
      
      try {
        localStorage.setItem(key, stringified);
        console.log(`Stored ${key} in localStorage (${(stringified.length/1024).toFixed(2)}KB)`);
      } catch (storageError) {
        console.error(`LocalStorage error: ${storageError instanceof Error ? storageError.message : String(storageError)}`);
        if (storageError instanceof DOMException && (
          // everything except Firefox
          storageError.code === 22 ||
          // Firefox
          storageError.code === 1014 ||
          // test name field too, because code might not be present
          storageError.name === 'QuotaExceededError' ||
          storageError.name === 'NS_ERROR_DOM_QUOTA_REACHED'
        )) {
          console.error('localStorage quota exceeded');
          // Maybe implement a fallback or cleanup old data
        }
      }
    }
  } catch (error) {
    console.error(`Error preparing ${key} for localStorage:`, error);
  }
};

// Hook for user data
export function useUser() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate fetching user data
    const storedUser = getFromStorage<User | null>('user', null);
    
    // If no stored user, create a demo user
    if (!storedUser) {
      const demoUser: User = {
        id: uuidv4(),
        name: 'Demo User',
        email: '<EMAIL>',
        avatar: '/placeholder-user.jpg',
        role: 'admin',
      };
      setInStorage('user', demoUser);
      setUser(demoUser);
    } else {
      setUser(storedUser);
    }
    
    setLoading(false);
  }, []);
  
  const updateUser = (updates: Partial<User>) => {
    if (!user) return;
    const updatedUser = { ...user, ...updates };
    setUser(updatedUser);
    setInStorage('user', updatedUser);
  };
  
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };
  
  return { user, loading, updateUser, logout };
}

// Hooks for assets
export function useAssets() {
  console.log('🎯🎯🎯 useAssets hook called - initializing... 🎯🎯🎯');

  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);
  const [offset, setOffset] = useState(0);
  const [dateFilters, setDateFilters] = useState<{year?: string; month?: string; day?: string}>({});
  const [scanProgress, setScanProgress] = useState<{ total: number; current: number; isIndexing: boolean }>({
    total: 0,
    current: 0,
    isIndexing: false
  });
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);


  // Use ref to track if we've already attempted auto-load (survives StrictMode)
  const autoLoadAttemptedRef = useRef(false);

  // Debug log temporarily removed to fix caching issue



  // Function to fetch indexing status from the API
  const fetchIndexingStatus = async () => {
    try {
      const response = await fetch('/api/photos/status');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Use string values for large numbers if available
          const indexed = data.indexedStr ? parseInt(data.indexedStr, 10) : (data.indexed || 0);
          const total = data.totalStr ? parseInt(data.totalStr, 10) : (data.total || 0);
          
          setScanProgress({
            total: total,
            current: indexed,
            isIndexing: data.isIndexing || false
          });
          return data.isIndexing; // Return the indexing status
        }
      }
      return false;
    } catch (error) {
      console.error('Error fetching indexing status:', error);
      return false;
    }
  };
  


  // Set up polling for indexing status
  useEffect(() => {
    // Fetch immediately on component mount
    fetchIndexingStatus();

    // Set up polling interval - every 3 seconds
    const interval = setInterval(() => {
      fetchIndexingStatus();
    }, 3000);

    // Clear interval on component unmount
    return () => clearInterval(interval);
  }, []);

  // Auto-load is handled by DashboardView component, not here



  // Manual fetch function - no automatic loading
  const fetchAssets = useCallback(async (resetOffset = false, forceRefresh = false) => {
    // Prevent concurrent requests (unless it's a forced refresh after modal completion)
    if (loading && !forceRefresh) {
      console.log('🚫 fetchAssets: Already loading, skipping request');
      return;
    }

    console.log('🚀 fetchAssets called!', { resetOffset, forceRefresh, currentAssetsLength: assets.length, loading, offset });

    const currentOffset = resetOffset ? 0 : offset;

    try {
      console.log('🔄 Setting loading to true...');
      setLoading(true);
      setError(null); // Clear any previous errors
      setHasAttemptedLoad(true); // Mark that we've attempted to load

      // Add a small delay to make loading state visible
      const delay = assets.length === 0 ? 300 : 200; // Longer delay for initial load
      await new Promise(resolve => setTimeout(resolve, delay));

      // Reset offset if requested
      if (resetOffset) {
        setOffset(0);
      }

      console.log('🔄 Starting to fetch assets...', { currentOffset, dateFilters });

      // Build URL with params
      const params = new URLSearchParams({
        limit: '50',
        offset: currentOffset.toString(),
        _t: Date.now().toString(), // Cache busting parameter
      });

      // Add date filters if present
      if (dateFilters.year) params.append('year', dateFilters.year);
      if (dateFilters.month) params.append('month', dateFilters.month);
      if (dateFilters.day) params.append('day', dateFilters.day);

      const url = `/api/photos?${params.toString()}`;
      console.log(`📸 ===== FETCH ASSETS API CALL =====`);
      console.log(`📸 Fetching assets from: ${url}`);
      console.log(`📸 Current dateFilters:`, dateFilters);
      console.log(`📸 Params:`, Object.fromEntries(params));



      const response = await fetch(url, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      console.log('📡 Response status:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`Failed to fetch assets: ${response.status} ${response.statusText}`);
      }
        
        const data = await response.json();
        console.log('📦 API Response data:', {
          success: data.success,
          assetsCount: data.assets?.length,
          total: data.total,
          firstAsset: data.assets?.[0]?.filename,
          hasMore: data.hasMore
        });
        console.log('📦 ===== END FETCH ASSETS API CALL =====');

        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch assets');
        }

        if (!data.assets || !Array.isArray(data.assets)) {
          console.error('❌ Invalid assets data:', data);
          throw new Error('Invalid assets data received from API');
        }

        // Convert storage assets to app assets
        const newAssets = data.assets.map((asset: any) => ({
          id: asset.id,
          title: asset.filename || 'Unknown',
          description: '',
          filename: asset.filename || 'Unknown',
          fileUrl: asset.fileUrl || '',
          thumbnailUrl: asset.thumbnailUrl || '',
          filePath: asset.filePath || '',
          type: asset.type || 'image',
          size: asset.size || 0,
          createdAt: asset.lastModified || asset.createdAt || new Date().toISOString(),
          updatedAt: asset.lastModified || asset.updatedAt || new Date().toISOString(),
          metadata: asset.metadata || {},
          tags: [],
        }));
        
        // If currentOffset is 0, replace all assets; otherwise append
        console.log('📝 About to set assets...', { currentOffset, newAssetsLength: newAssets.length, currentAssetsLength: assets.length });
        if (currentOffset === 0) {
          console.log('🔄 Replacing all assets with new ones');
          setAssets(newAssets);
          console.log('✅ setAssets called with', newAssets.length, 'assets');
        } else {
          console.log('➕ Appending new assets to existing ones');
          setAssets(prev => {
            const combined = [...prev, ...newAssets];
            console.log('✅ setAssets called with combined', combined.length, 'assets');
            return combined;
          });
        }

        console.log('📊 Setting total and hasMore...', { total: data.total, hasMore: data.hasMore });
        setTotal(data.total || 0);
        setHasMore(data.hasMore || false);
        setError(null);

        console.log(`✅ Loaded ${newAssets.length} assets (${data.total} total)`);
        console.log('First few assets:', newAssets.slice(0, 3));
        console.log('📊 Final state should be:', { assetsLength: newAssets.length, total: data.total });

    } catch (err) {
      console.error('Error fetching assets:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch assets');
    } finally {
      setLoading(false);
    }
  }, [loading, offset, dateFilters.year, dateFilters.month, dateFilters.day]);

  const loadMore = async () => {
    if (!loading && hasMore) {
      const newOffset = offset + 50;
      setOffset(newOffset);
      // Trigger fetch with the new offset
      await fetchAssets(false);
    }
  };

  const setFilters = (filters: {year?: string; month?: string; day?: string}) => {
    console.log('🔧 ===== STORE setFilters CALLED =====');
    console.log('🔧 New filters:', filters);
    console.log('🔧 Previous filters:', dateFilters);
    console.log('🔧 Current assets count:', assets.length);

    // Clear current assets and reset pagination
    setAssets([]);
    setOffset(0);
    setHasMore(true);

    // Update filters - this will trigger DashboardView useEffect
    setDateFilters(filters);

    console.log('🔧 Store updated - assets cleared, filters set');
    console.log('🔧 ===== END STORE setFilters =====');
  };

  const addAsset = (asset: Omit<Asset, "id" | "createdAt" | "updatedAt">) => {
    const newAsset: Asset = {
      ...asset,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setAssets(prev => [newAsset, ...prev]);
    return newAsset;
  };

  const updateAsset = (id: string, updates: Partial<Asset>) => {
    setAssets(prev => prev.map(asset => 
      asset.id === id ? { ...asset, ...updates, updatedAt: new Date().toISOString() } : asset
    ));
  };

  const deleteAsset = (id: string) => {
    setAssets(prev => prev.filter(asset => asset.id !== id));
  };

  const getAsset = (id: string) => {
    return assets.find(asset => asset.id === id);
  };

  const refreshAssets = async () => {
    setOffset(0);
    setLoading(true);
    setError(null);

    try {
      // Build URL with refresh parameter and cache busting
      const params = new URLSearchParams({
        limit: '50',
        offset: '0',
        refresh: 'true',  // Force refresh to trigger indexing
        _t: Date.now().toString(), // Cache busting parameter
      });

      // Add date filters if present
      if (dateFilters.year) params.append('year', dateFilters.year);
      if (dateFilters.month) params.append('month', dateFilters.month);
      if (dateFilters.day) params.append('day', dateFilters.day);

      const url = `/api/photos?${params.toString()}`;
      console.log('🔄 Refreshing assets with forced indexing and cache busting...');

      const response = await fetch(url, {
        cache: 'no-cache', // Ensure fresh data
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to refresh assets: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to refresh assets');
      }

      // Convert storage assets to app assets
      const appAssets = data.assets.map((asset: any) => ({
        id: asset.id,
        title: asset.filename || 'Unknown',
        description: '',
        filename: asset.filename || 'Unknown',
        fileUrl: asset.fileUrl || '',
        thumbnailUrl: asset.thumbnailUrl || '',
        filePath: asset.filePath || '',
        type: asset.type || 'image',
        size: asset.size || 0,
        createdAt: asset.lastModified || asset.createdAt || new Date().toISOString(),
        updatedAt: asset.lastModified || asset.updatedAt || new Date().toISOString(),
        metadata: asset.metadata || {},
        tags: [],
      }));

      // Replace all assets with fresh data
      setAssets(appAssets);
      setTotal(data.total || 0);
      setHasMore(appAssets.length === 50);
      setError(null);

      console.log(`✅ Refreshed ${appAssets.length} assets (${data.total} total)`);

      // Trigger asset stats refresh after successful asset refresh
      window.dispatchEvent(new CustomEvent('assetsRefreshed'));

    } catch (err) {
      console.error('Error refreshing assets:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh assets');
    } finally {
      setLoading(false);
    }
  };



  return {
    assets,
    loading,
    error,
    hasMore,
    total,
    loadMore,
    addAsset,
    updateAsset,
    deleteAsset,
    getAsset,
    refreshAssets,
    setFilters,
    dateFilters,
    scanProgress,
    fetchAssets, // Manual fetch function
    hasAttemptedLoad
  };
}

export function useCollections() {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const storedCollections = getFromStorage<Collection[]>('collections', []);
    setCollections(storedCollections);
    setLoading(false);
  }, []);
  
  const addCollection = (collection: Omit<Collection, "id">) => {
    const newCollection: Collection = {
      ...collection,
      id: uuidv4(),
    };
    
    const updatedCollections = [newCollection, ...collections];
    setCollections(updatedCollections);
    setInStorage('collections', updatedCollections);
    return newCollection;
  };
  
  const updateCollection = (id: string, updates: Partial<Collection>) => {
    const updatedCollections = collections.map(collection => 
      collection.id === id ? { ...collection, ...updates } : collection
    );
    setCollections(updatedCollections);
    setInStorage('collections', updatedCollections);
  };
  
  const deleteCollection = (id: string) => {
    const updatedCollections = collections.filter(collection => collection.id !== id);
    setCollections(updatedCollections);
    setInStorage('collections', updatedCollections);
  };
  
  const getCollection = (id: string) => {
    return collections.find(collection => collection.id === id);
  };
  
  return {
    collections,
    loading,
    addCollection,
    updateCollection,
    deleteCollection,
    getCollection,
  };
}

export function useTags() {
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tags from database instead of localStorage
  const fetchTags = async (includeUsage = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/tags?includeUsage=${includeUsage}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch tags');
      }

      setTags(data.tags || []);
    } catch (err) {
      console.error('Error fetching tags:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch tags');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  const addTag = async (tag: Omit<Tag, "id">) => {
    try {
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tag),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create tag');
      }

      // Add to local state
      setTags(prev => [data.tag, ...prev]);
      return data.tag;
    } catch (err) {
      console.error('Error creating tag:', err);
      throw err;
    }
  };

  const updateTag = async (id: string, updates: Partial<Tag>) => {
    try {
      const response = await fetch('/api/tags', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, ...updates }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update tag');
      }

      // Update local state
      setTags(prev => prev.map(tag =>
        tag.id === id ? { ...tag, ...updates } : tag
      ));

      return data.tag;
    } catch (err) {
      console.error('Error updating tag:', err);
      throw err;
    }
  };

  const deleteTag = async (id: string) => {
    try {
      const response = await fetch(`/api/tags?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete tag');
      }

      // Remove from local state
      setTags(prev => prev.filter(tag => tag.id !== id));
    } catch (err) {
      console.error('Error deleting tag:', err);
      throw err;
    }
  };

  return {
    tags,
    loading,
    error,
    fetchTags,
    addTag,
    updateTag,
    deleteTag,
  };
}

// Hook for asset statistics
export function useAssetStats() {
  const [stats, setStats] = useState({
    total: 0,
    images: 0,
    videos: 0,
    audio: 0,
    documents: 0,
    byType: {} as Record<string, number>
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      console.log('🔢 useAssetStats: fetchStats called');
      setLoading(true);
      setError(null);

      console.log('🔢 useAssetStats: Making API request to /api/assets/stats');
      const response = await fetch('/api/assets/stats');
      const data = await response.json();

      console.log('🔢 useAssetStats: API response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch asset statistics');
      }

      const newStats = data.stats || {
        total: 0,
        images: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        byType: {}
      };

      console.log('🔢 useAssetStats: Setting stats:', newStats);
      setStats(newStats);
    } catch (err) {
      console.error('Error fetching asset statistics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch asset statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔢 useAssetStats: useEffect called, fetching stats...');
    fetchStats();

    // Listen for asset refresh events to update stats
    const handleAssetsRefreshed = () => {
      console.log('🔢 useAssetStats: assetsRefreshed event received');
      fetchStats();
    };

    window.addEventListener('assetsRefreshed', handleAssetsRefreshed);

    return () => {
      window.removeEventListener('assetsRefreshed', handleAssetsRefreshed);
    };
  }, []);

  return {
    stats,
    loading,
    error,
    fetchStats
  };
}

// Hook for managing asset tags
export function useAssetTags(assetId?: string) {
  const [assetTags, setAssetTags] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch tags for a specific asset
  const fetchAssetTags = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assets/${id}/tags`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch asset tags');
      }

      setAssetTags(data.tags || []);
      return data.tags;
    } catch (err) {
      console.error('Error fetching asset tags:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch asset tags');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Add tags to an asset
  const addTagsToAsset = async (id: string, tagIds: string[], isAutoGenerated = false, confidence = 1.0) => {
    try {
      const response = await fetch(`/api/assets/${id}/tags`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tagIds, isAutoGenerated, confidence }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add tags to asset');
      }

      // Refresh asset tags if this is the current asset
      if (id === assetId) {
        await fetchAssetTags(id);
      }

      return data;
    } catch (err) {
      console.error('Error adding tags to asset:', err);
      throw err;
    }
  };

  // Remove tags from an asset
  const removeTagsFromAsset = async (id: string, tagIds: string[]) => {
    try {
      const response = await fetch(`/api/assets/${id}/tags?tagIds=${tagIds.join(',')}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to remove tags from asset');
      }

      // Refresh asset tags if this is the current asset
      if (id === assetId) {
        await fetchAssetTags(id);
      }

      return data;
    } catch (err) {
      console.error('Error removing tags from asset:', err);
      throw err;
    }
  };

  // Auto-tag an asset using AI
  const autoTagAsset = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assets/${id}/auto-tag`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to auto-tag asset');
      }

      // Refresh asset tags if this is the current asset
      if (id === assetId) {
        await fetchAssetTags(id);
      }

      return data;
    } catch (err) {
      console.error('Error auto-tagging asset:', err);
      setError(err instanceof Error ? err.message : 'Failed to auto-tag asset');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Batch auto-tag multiple assets
  const batchAutoTag = async (assetIds: string[]) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/assets/batch-auto-tag', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ assetIds }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to batch auto-tag assets');
      }

      return data;
    } catch (err) {
      console.error('Error batch auto-tagging assets:', err);
      setError(err instanceof Error ? err.message : 'Failed to batch auto-tag assets');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch tags when assetId changes
  useEffect(() => {
    if (assetId) {
      fetchAssetTags(assetId);
    }
  }, [assetId]);

  return {
    assetTags,
    loading,
    error,
    fetchAssetTags,
    addTagsToAsset,
    removeTagsFromAsset,
    autoTagAsset,
    batchAutoTag,
  };
}

export function useSearch(dateFilters: {year?: string; month?: string; day?: string} = {}) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Asset[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchCache] = useState(new Map<string, { results: Asset[]; timestamp: number }>());
  const [lastSearchWasCached, setLastSearchWasCached] = useState(false);

  // Debounced search function that includes date filters
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      if (query.trim()) {
        console.log('🔍 Triggering search with date filters:', dateFilters);
        search(query);
      } else {
        setSearchResults([]);
      }
    }, 300),
    [dateFilters] // Re-create when date filters change
  );

  // Re-trigger search when date filters change (if there's an active search)
  useEffect(() => {
    if (searchQuery.trim()) {
      console.log('🗓️ Date filters changed, re-triggering search for:', searchQuery, 'with new filters:', dateFilters);
      // Clear cache when filters change to force fresh results
      searchCache.clear();
      debouncedSearch(searchQuery);
    }
  }, [dateFilters.year, dateFilters.month, dateFilters.day, searchQuery, debouncedSearch]);

  // Auto-search when query changes
  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);
  
  const search = async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    // Create cache key including date filters
    const cacheKey = `${term.trim()}-${dateFilters.year || ''}-${dateFilters.month || ''}-${dateFilters.day || ''}`;
    console.log('🔑 Cache key:', cacheKey);
    console.log('🗓️ Current date filters:', dateFilters);

    // Check cache first (5 minute TTL)
    const cached = searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      console.log('🎯 Using cached search results for:', term, 'with filters:', dateFilters);
      setSearchResults(cached.results);
      setLastSearchWasCached(true);
      return;
    } else {
      console.log('🆕 No cache hit, making fresh API call for:', term, 'with filters:', dateFilters);
    }

    setIsSearching(true);

    try {
      // Build search URL with date filters
      const params = new URLSearchParams({
        q: term
      });

      // Add date filters if present
      if (dateFilters.year) params.append('year', dateFilters.year);
      if (dateFilters.month) params.append('month', dateFilters.month);
      if (dateFilters.day) params.append('day', dateFilters.day);

      const searchUrl = `/api/photos/search?${params.toString()}`;
      console.log('🔍 Searching with basic endpoint:', { term, dateFilters, url: searchUrl });

      // Make API call to basic search endpoint (faster)
      const response = await fetch(searchUrl);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Convert search results to app assets format
          const searchAssets = data.assets.map((asset: any) => ({
            id: asset.id,
            title: asset.filename || 'Unknown',
            description: '',
            filename: asset.filename || 'Unknown',
            fileUrl: asset.fileUrl || '',
            thumbnailUrl: asset.thumbnailUrl || '',
            filePath: asset.filePath || '',
            type: asset.type || 'image',
            size: asset.size || 0,
            createdAt: asset.lastModified || asset.createdAt || new Date().toISOString(),
            updatedAt: asset.lastModified || asset.updatedAt || new Date().toISOString(),
            metadata: asset.metadata || {},
            tags: [],
          }));
          setSearchResults(searchAssets);
          setLastSearchWasCached(false);

          // Cache the results
          searchCache.set(cacheKey, {
            results: searchAssets,
            timestamp: Date.now()
          });

          // Clean old cache entries (keep only last 20)
          if (searchCache.size > 20) {
            const entries = Array.from(searchCache.entries());
            entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
            searchCache.clear();
            entries.slice(0, 20).forEach(([key, value]) => {
              searchCache.set(key, value);
            });
          }
        } else {
          setSearchResults([]);
        }
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching,
    search,
    lastSearchWasCached,
  };
}
