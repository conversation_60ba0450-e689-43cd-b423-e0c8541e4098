import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { db } from '@/lib/db/schema';
import { folders, folderStats, assets } from '@/lib/db/schema';
import { eq, desc, and, or, sql, gt, like } from 'drizzle-orm';

export interface FolderInfo {
  id: string;
  path: string;
  parentPath: string | null;
  name: string;
  fileCount: number;
  imageCount: number;
  totalSize: string;
  lastModified: Date;
  lastScanned: Date;
  contentHash: string | null;
}

export interface FolderChangeInfo {
  newFiles: number;
  deletedFiles: number;
  modifiedFiles: number;
  hasChanges: boolean;
}

export class FolderTrackingService {
  private static instance: FolderTrackingService;
  private basePath: string;

  private constructor() {
    this.basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
  }

  static getInstance(): FolderTrackingService {
    if (!FolderTrackingService.instance) {
      FolderTrackingService.instance = new FolderTrackingService();
    }
    return FolderTrackingService.instance;
  }

  /**
   * Check if a file is an image file
   */
  private isImageFile(filename: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.svg'];
    const ext = path.extname(filename).toLowerCase();
    return imageExtensions.includes(ext);
  }

  /**
   * Calculate content hash for a folder based on file names and modification times
   */
  private async calculateFolderHash(folderPath: string): Promise<string> {
    try {
      const entries = await fs.promises.readdir(folderPath, { withFileTypes: true });
      const fileInfo: string[] = [];

      for (const entry of entries) {
        if (entry.isFile() && this.isImageFile(entry.name)) {
          const filePath = path.join(folderPath, entry.name);
          const stat = await fs.promises.stat(filePath);
          fileInfo.push(`${entry.name}:${stat.mtime.getTime()}:${stat.size}`);
        }
      }

      // Sort to ensure consistent hash regardless of file system order
      fileInfo.sort();
      const content = fileInfo.join('|');
      return crypto.createHash('sha256').update(content).digest('hex');
    } catch (error) {
      console.warn(`Error calculating hash for ${folderPath}:`, error);
      return '';
    }
  }

  /**
   * Scan a folder and get current statistics
   */
  private async scanFolderStats(folderPath: string): Promise<{
    fileCount: number;
    imageCount: number;
    totalSize: number;
    lastModified: Date;
  }> {
    let fileCount = 0;
    let imageCount = 0;
    let totalSize = 0;
    let lastModified = new Date(0);

    try {
      const entries = await fs.promises.readdir(folderPath, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isFile()) {
          const filePath = path.join(folderPath, entry.name);
          const stat = await fs.promises.stat(filePath);
          
          fileCount++;
          totalSize += stat.size;
          
          if (stat.mtime > lastModified) {
            lastModified = stat.mtime;
          }

          if (this.isImageFile(entry.name)) {
            imageCount++;
          }
        }
      }
    } catch (error) {
      console.warn(`Error scanning folder ${folderPath}:`, error);
    }

    return { fileCount, imageCount, totalSize, lastModified };
  }

  /**
   * Get or create folder record in database
   */
  async getOrCreateFolder(folderPath: string): Promise<FolderInfo> {
    const relativePath = path.relative(this.basePath, folderPath);
    const folderName = path.basename(folderPath);
    const parentPath = path.dirname(relativePath);
    const parentPathNormalized = parentPath === '.' ? null : parentPath;

    // Check if folder exists in database
    const existingFolder = await db.select().from(folders)
      .where(eq(folders.path, relativePath))
      .limit(1);

    if (existingFolder.length > 0) {
      return {
        id: existingFolder[0].id,
        path: existingFolder[0].path,
        parentPath: existingFolder[0].parentPath,
        name: existingFolder[0].name,
        fileCount: existingFolder[0].fileCount,
        imageCount: existingFolder[0].imageCount,
        totalSize: existingFolder[0].totalSize,
        lastModified: existingFolder[0].lastModified,
        lastScanned: existingFolder[0].lastScanned,
        contentHash: existingFolder[0].contentHash,
      };
    }

    // Create new folder record
    const stats = await this.scanFolderStats(folderPath);
    const contentHash = await this.calculateFolderHash(folderPath);
    const folderId = uuidv4();

    const newFolder = {
      id: folderId,
      path: relativePath,
      parentPath: parentPathNormalized,
      name: folderName,
      fileCount: stats.fileCount,
      imageCount: stats.imageCount,
      totalSize: stats.totalSize.toString(),
      lastModified: stats.lastModified,
      lastScanned: new Date(),
      contentHash: contentHash,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await db.insert(folders).values(newFolder);

    return {
      id: folderId,
      path: relativePath,
      parentPath: parentPathNormalized,
      name: folderName,
      fileCount: stats.fileCount,
      imageCount: stats.imageCount,
      totalSize: stats.totalSize.toString(),
      lastModified: stats.lastModified,
      lastScanned: new Date(),
      contentHash: contentHash,
    };
  }

  /**
   * Update folder statistics in database
   */
  async updateFolderStats(folderPath: string): Promise<FolderChangeInfo> {
    const relativePath = path.relative(this.basePath, folderPath);
    
    // Get current folder stats from database
    const dbFolder = await db.select().from(folders)
      .where(eq(folders.path, relativePath))
      .limit(1);

    if (dbFolder.length === 0) {
      // Folder doesn't exist in database, create it
      await this.getOrCreateFolder(folderPath);
      return { newFiles: 0, deletedFiles: 0, modifiedFiles: 0, hasChanges: true };
    }

    const folder = dbFolder[0];
    const currentStats = await this.scanFolderStats(folderPath);
    const currentHash = await this.calculateFolderHash(folderPath);

    // Check if folder has changed
    const hasChanges = 
      folder.fileCount !== currentStats.fileCount ||
      folder.imageCount !== currentStats.imageCount ||
      folder.contentHash !== currentHash;

    if (!hasChanges) {
      // Update last scanned time even if no changes
      await db.update(folders)
        .set({ lastScanned: new Date(), updatedAt: new Date() })
        .where(eq(folders.id, folder.id));
      
      return { newFiles: 0, deletedFiles: 0, modifiedFiles: 0, hasChanges: false };
    }

    // Calculate changes
    const newFiles = Math.max(0, currentStats.imageCount - folder.imageCount);
    const deletedFiles = Math.max(0, folder.imageCount - currentStats.imageCount);
    const modifiedFiles = 0; // We'll implement this later with more detailed tracking

    // Update folder record
    await db.update(folders)
      .set({
        fileCount: currentStats.fileCount,
        imageCount: currentStats.imageCount,
        totalSize: currentStats.totalSize.toString(),
        lastModified: currentStats.lastModified,
        lastScanned: new Date(),
        contentHash: currentHash,
        updatedAt: new Date(),
      })
      .where(eq(folders.id, folder.id));

    // Record statistics
    await this.recordFolderStats(folder.id, {
      fileCount: currentStats.fileCount,
      imageCount: currentStats.imageCount,
      totalSize: currentStats.totalSize.toString(),
      newFiles,
      deletedFiles,
      modifiedFiles,
    });

    return { newFiles, deletedFiles, modifiedFiles, hasChanges: true };
  }

  /**
   * Record folder statistics for historical tracking
   */
  private async recordFolderStats(folderId: string, stats: {
    fileCount: number;
    imageCount: number;
    totalSize: number;
    newFiles: number;
    deletedFiles: number;
    modifiedFiles: number;
  }): Promise<void> {
    const statId = uuidv4();
    
    await db.insert(folderStats).values({
      id: statId,
      folderId: folderId,
      statDate: new Date(),
      fileCount: stats.fileCount,
      imageCount: stats.imageCount,
      totalSize: stats.totalSize.toString(),
      newFiles: stats.newFiles,
      deletedFiles: stats.deletedFiles,
      modifiedFiles: stats.modifiedFiles,
      createdAt: new Date(),
    });
  }

  /**
   * Get folders that need scanning based on modification time
   */
  async getFoldersNeedingUpdate(cutoffTime: Date): Promise<string[]> {
    const foldersToUpdate: string[] = [];

    // Get all folders from database
    const dbFolders = await db.select().from(folders);

    for (const folder of dbFolders) {
      const fullPath = path.join(this.basePath, folder.path);
      
      try {
        // Check if folder still exists
        const stat = await fs.promises.stat(fullPath);
        
        // Check if folder has been modified since cutoff time (recent changes)
        // OR if it hasn't been scanned recently (needs periodic check)
        if (stat.mtime >= cutoffTime || folder.lastScanned < cutoffTime) {
          foldersToUpdate.push(fullPath);
          console.log(`📁 Folder needs update: ${path.basename(fullPath)} (modified: ${stat.mtime.toISOString()}, cutoff: ${cutoffTime.toISOString()})`);
        }
      } catch (error) {
        // Folder doesn't exist anymore, mark for cleanup
        console.log(`📁 Folder no longer exists: ${folder.path}`);
        await this.cleanupDeletedFolder(folder.id);
      }
    }

    return foldersToUpdate;
  }

  /**
   * Clean up deleted folder from database
   */
  private async cleanupDeletedFolder(folderId: string): Promise<void> {
    try {
      // First get the folder path to clean up associated assets
      const folderRecord = await db.select().from(folders)
        .where(eq(folders.id, folderId))
        .limit(1);

      if (folderRecord.length > 0) {
        const folderPath = folderRecord[0].path;

        // Clean up all assets that belong to this folder
        const deletedAssets = await db.delete(assets)
          .where(sql`file_path LIKE ${folderPath + '%'}`)
          .returning({ id: assets.id, filename: assets.filename });

        if (deletedAssets.length > 0) {
          console.log(`🗑️ Cleaned up ${deletedAssets.length} orphaned assets from deleted folder: ${folderPath}`);
        }
      }

      // Then delete the folder record
      await db.delete(folders).where(eq(folders.id, folderId));
      console.log(`🗑️ Cleaned up deleted folder from database`);
    } catch (error) {
      console.error('Error cleaning up deleted folder:', error);
    }
  }

  /**
   * Get folder statistics
   */
  async getFolderStats(folderPath: string): Promise<FolderInfo | null> {
    const relativePath = path.relative(this.basePath, folderPath);
    
    const result = await db.select().from(folders)
      .where(eq(folders.path, relativePath))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    const folder = result[0];
    return {
      id: folder.id,
      path: folder.path,
      parentPath: folder.parentPath,
      name: folder.name,
      fileCount: folder.fileCount,
      imageCount: folder.imageCount,
      totalSize: folder.totalSize,
      lastModified: folder.lastModified,
      lastScanned: folder.lastScanned,
      contentHash: folder.contentHash,
    };
  }

  /**
   * Get all folders with their statistics
   */
  async getAllFolders(): Promise<FolderInfo[]> {
    const result = await db.select().from(folders)
      .orderBy(folders.path);

    return result.map(folder => ({
      id: folder.id,
      path: folder.path,
      parentPath: folder.parentPath,
      name: folder.name,
      fileCount: folder.fileCount,
      imageCount: folder.imageCount,
      totalSize: folder.totalSize,
      lastModified: folder.lastModified,
      lastScanned: folder.lastScanned,
      contentHash: folder.contentHash,
    }));
  }

  /**
   * Initialize folder tracking by scanning all directories
   */
  async initializeFolderTracking(): Promise<void> {
    console.log('📁 Initializing folder tracking system...');

    try {
      // Recursively scan all directories
      await this.scanDirectoryRecursively(this.basePath);
      console.log('✅ Folder tracking initialization completed');
    } catch (error) {
      console.error('❌ Error initializing folder tracking:', error);
      throw error;
    }
  }

  /**
   * Recursively scan directories and create folder records
   */
  private async scanDirectoryRecursively(dirPath: string): Promise<void> {
    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

      // Check if this directory has any files
      const hasFiles = entries.some(entry => entry.isFile());

      if (hasFiles) {
        // Create or update folder record for this directory
        await this.getOrCreateFolder(dirPath);
      }

      // Recursively scan subdirectories
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const subDirPath = path.join(dirPath, entry.name);
          await this.scanDirectoryRecursively(subDirPath);
        }
      }
    } catch (error) {
      console.warn(`Error scanning directory ${dirPath}:`, error);
    }
  }

  /**
   * Update statistics for all folders
   */
  async updateAllFolderStats(): Promise<number> {
    console.log('📊 Updating statistics for all folders...');

    try {
      const allFolders = await this.getAllFolders();
      let updatedCount = 0;

      for (const folder of allFolders) {
        try {
          const fullPath = path.join(this.basePath, folder.path);

          // Check if folder still exists
          if (fs.existsSync(fullPath)) {
            const changeInfo = await this.updateFolderStats(fullPath);
            if (changeInfo.hasChanges) {
              updatedCount++;
            }
          } else {
            // Folder no longer exists, clean it up
            await this.cleanupDeletedFolder(folder.id);
            updatedCount++;
          }
        } catch (error) {
          console.warn(`Error updating stats for folder ${folder.path}:`, error);
        }
      }

      console.log(`✅ Updated statistics for ${updatedCount} folders`);
      return updatedCount;
    } catch (error) {
      console.error('❌ Error updating folder statistics:', error);
      throw error;
    }
  }
}
