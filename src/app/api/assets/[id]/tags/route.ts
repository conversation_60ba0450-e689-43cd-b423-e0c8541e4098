import { NextRequest, NextResponse } from 'next/server';
import { db, tags, assetTags, assets } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

// GET /api/assets/[id]/tags - Get all tags for a specific asset
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: assetId } = await params;

    // Get all tags for this asset with their details
    const assetTagsWithDetails = await db
      .select({
        tagId: tags.id,
        tagName: tags.name,
        tagColor: tags.color,
        tagDescription: tags.description,
        isAutoGenerated: assetTags.isAutoGenerated,
        confidence: assetTags.confidence,
        createdAt: assetTags.createdAt,
      })
      .from(assetTags)
      .innerJoin(tags, eq(assetTags.tagId, tags.id))
      .where(eq(assetTags.assetId, assetId))
      .orderBy(tags.name);

    return NextResponse.json({
      success: true,
      assetId,
      tags: assetTagsWithDetails,
    });

  } catch (error) {
    console.error('Error fetching asset tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch asset tags', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// POST /api/assets/[id]/tags - Add tags to an asset
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: assetId } = await params;
    const body = await request.json();
    const { tagIds, confidence = 1.0, isAutoGenerated = false } = body;

    if (!Array.isArray(tagIds) || tagIds.length === 0) {
      return NextResponse.json(
        { error: 'tagIds must be a non-empty array' },
        { status: 400 }
      );
    }

    // Check if asset exists
    const asset = await db
      .select()
      .from(assets)
      .where(eq(assets.id, assetId))
      .limit(1);

    if (asset.length === 0) {
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    // Check if all tags exist
    const existingTags = await db
      .select()
      .from(tags)
      .where(eq(tags.id, tagIds[0])); // We'll check each one individually

    const validTagIds = [];
    for (const tagId of tagIds) {
      const tagExists = await db
        .select()
        .from(tags)
        .where(eq(tags.id, tagId))
        .limit(1);
      
      if (tagExists.length > 0) {
        validTagIds.push(tagId);
      }
    }

    if (validTagIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid tags found' },
        { status: 400 }
      );
    }

    // Add tags to asset (ignore duplicates)
    const assetTagsToInsert = validTagIds.map(tagId => ({
      assetId,
      tagId,
      confidence: Number(confidence),
      isAutoGenerated: Boolean(isAutoGenerated),
    }));

    const insertedTags = [];
    for (const assetTag of assetTagsToInsert) {
      try {
        const [inserted] = await db
          .insert(assetTags)
          .values(assetTag)
          .returning();
        insertedTags.push(inserted);
      } catch (error) {
        // Ignore duplicate key errors (tag already exists for this asset)
        if (!error.message?.includes('duplicate key')) {
          throw error;
        }
      }
    }

    return NextResponse.json({
      success: true,
      assetId,
      addedTags: insertedTags.length,
      skippedDuplicates: assetTagsToInsert.length - insertedTags.length,
    });

  } catch (error) {
    console.error('Error adding tags to asset:', error);
    return NextResponse.json(
      { error: 'Failed to add tags to asset', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// DELETE /api/assets/[id]/tags - Remove tags from an asset
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: assetId } = await params;
    const { searchParams } = new URL(request.url);
    const tagIds = searchParams.get('tagIds')?.split(',') || [];

    if (tagIds.length === 0) {
      return NextResponse.json(
        { error: 'At least one tagId must be provided' },
        { status: 400 }
      );
    }

    // Remove tags from asset
    let deletedCount = 0;
    for (const tagId of tagIds) {
      const result = await db
        .delete(assetTags)
        .where(and(
          eq(assetTags.assetId, assetId),
          eq(assetTags.tagId, tagId)
        ));
      
      // Note: Drizzle doesn't return affected rows count directly
      deletedCount++;
    }

    return NextResponse.json({
      success: true,
      assetId,
      removedTags: deletedCount,
    });

  } catch (error) {
    console.error('Error removing tags from asset:', error);
    return NextResponse.json(
      { error: 'Failed to remove tags from asset', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
