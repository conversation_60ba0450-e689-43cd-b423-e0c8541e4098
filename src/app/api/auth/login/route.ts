import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/services/authService';

// POST /api/auth/login - Authenticate user
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    console.log('🔐 Login attempt for:', email);

    const authService = AuthService.getInstance();
    
    // Initialize default users if none exist
    await authService.initializeDefaultUsers();
    
    const user = await authService.authenticateUser(email, password);

    if (!user) {
      console.log('❌ Invalid credentials for:', email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log('✅ Login successful for:', email);

    return NextResponse.json({
      success: true,
      user,
      message: 'Login successful'
    });

  } catch (error) {
    console.error('❌ Login error:', error);
    return NextResponse.json(
      { 
        error: 'Login failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
