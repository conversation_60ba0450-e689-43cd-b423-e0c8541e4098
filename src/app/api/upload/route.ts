import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { IndexingService } from '@/lib/storage/indexingService';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string;
    const createFolder = formData.get('createFolder') === 'true';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!folder) {
      return NextResponse.json({ error: 'No folder specified' }, { status: 400 });
    }

    // Get the storage base path
    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    const targetFolderPath = path.join(storagePath, folder);

    // Create folder if it doesn't exist and createFolder is true
    if (createFolder || !existsSync(targetFolderPath)) {
      console.log(`📁 Creating folder: ${targetFolderPath}`);
      await mkdir(targetFolderPath, { recursive: true });
    }

    // Generate unique filename to avoid conflicts
    const timestamp = Date.now();
    const fileExtension = path.extname(file.name);
    const baseName = path.basename(file.name, fileExtension);
    const uniqueFileName = `${baseName}-${timestamp}${fileExtension}`;
    const filePath = path.join(targetFolderPath, uniqueFileName);

    // Convert file to buffer and write to disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    console.log(`💾 Saving file: ${filePath}`);
    await writeFile(filePath, buffer);

    // Index the new file immediately
    console.log(`🔍 Indexing new file: ${filePath}`);
    try {
      // Create indexing service instance and process the new file
      const indexingService = new IndexingService();
      await indexingService.indexSingleFile(filePath);
      console.log(`✅ Successfully indexed: ${uniqueFileName}`);
    } catch (indexError) {
      console.error(`❌ Error indexing file ${uniqueFileName}:`, indexError);
      // Don't fail the upload if indexing fails
    }

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        name: uniqueFileName,
        originalName: file.name,
        size: file.size,
        folder: folder,
        path: path.relative(storagePath, filePath)
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { 
        error: 'Upload failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check upload status or list recent uploads
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'Upload endpoint is ready',
      supportedFormats: ['image/*', 'video/*', 'audio/*', 'application/pdf'],
      maxFileSize: '100MB',
      features: [
        'Folder selection',
        'New folder creation',
        'Batch processing',
        'Automatic indexing',
        'EXIF extraction'
      ]
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get upload info' },
      { status: 500 }
    );
  }
}
