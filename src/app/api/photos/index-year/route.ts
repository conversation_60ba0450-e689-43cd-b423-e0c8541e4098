import { NextResponse } from 'next/server';
import { IndexingService } from '@/lib/storage/indexingService';
import fs from 'fs';
import path from 'path';

const indexingService = IndexingService.getInstance();

export async function POST(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const year = searchParams.get('year');
    
    if (!year) {
      return NextResponse.json({
        success: false,
        error: 'Year parameter is required'
      }, { status: 400 });
    }
    
    console.log(`🎯 Starting targeted indexing for year: ${year}`);
    
    // Get the base storage path
    const basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    const yearPath = path.join(basePath, year);
    
    // Check if year folder exists
    if (!fs.existsSync(yearPath)) {
      return NextResponse.json({
        success: false,
        error: `Year folder ${year} does not exist`,
        path: yearPath
      }, { status: 404 });
    }
    
    // Get folder stats
    const stats = fs.statSync(yearPath);
    if (!stats.isDirectory()) {
      return NextResponse.json({
        success: false,
        error: `${year} is not a directory`
      }, { status: 400 });
    }
    
    console.log(`📁 Found year folder: ${yearPath}`);
    
    // Start targeted indexing for this year
    const startTime = Date.now();
    let processedFiles = 0;
    
    try {
      // Use the existing indexDirectory method but target specific year
      processedFiles = await indexingService.indexDirectory(yearPath);
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`✅ Completed indexing year ${year}: ${processedFiles} files in ${duration}s`);
      
      return NextResponse.json({
        success: true,
        message: `Successfully indexed year ${year}`,
        year,
        processedFiles,
        duration: `${duration}s`,
        path: yearPath
      });
      
    } catch (indexError) {
      console.error(`❌ Error indexing year ${year}:`, indexError);
      return NextResponse.json({
        success: false,
        error: `Failed to index year ${year}`,
        details: indexError instanceof Error ? indexError.message : String(indexError)
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error in targeted year indexing:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process targeted year indexing request',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
