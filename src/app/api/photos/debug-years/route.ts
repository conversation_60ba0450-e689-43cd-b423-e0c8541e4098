import { NextResponse } from 'next/server';
import { db } from '@/lib/db/schema';
import { assets } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('🔍 Debugging year distribution in database...');
    
    // Get year distribution from database
    const yearDistribution = await db
      .select({
        year: sql<string>`EXTRACT(YEAR FROM last_modified)::text`,
        count: sql<number>`COUNT(*)::int`
      })
      .from(assets)
      .groupBy(sql`EXTRACT(YEAR FROM last_modified)`)
      .orderBy(sql`EXTRACT(YEAR FROM last_modified) DESC`);
    
    console.log('📊 Year distribution:', yearDistribution);
    
    // Get sample assets from each year
    const sampleAssets = await db
      .select({
        id: assets.id,
        filename: assets.filename,
        path: assets.path,
        lastModified: assets.lastModified
      })
      .from(assets)
      .where(sql`EXTRACT(YEAR FROM last_modified) IN (2021, 2023, 2024)`)
      .limit(10);
    
    console.log('📁 Sample assets from missing years:', sampleAssets);
    
    // Check for assets with paths containing 2021, 2023, 2024
    const pathSamples = await db
      .select({
        id: assets.id,
        filename: assets.filename,
        path: assets.path,
        lastModified: assets.lastModified
      })
      .from(assets)
      .where(sql`path LIKE '%/2021/%' OR path LIKE '%/2023/%' OR path LIKE '%/2024/%'`)
      .limit(20);
    
    console.log('🗂️ Assets with 2021/2023/2024 in path:', pathSamples);
    
    return NextResponse.json({
      success: true,
      message: 'Year distribution debug completed',
      yearDistribution,
      sampleAssets,
      pathSamples,
      totalAssets: yearDistribution.reduce((sum, item) => sum + item.count, 0)
    });
  } catch (error) {
    console.error('❌ Error debugging years:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to debug years',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
