'use client'

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { ChevronRight, FolderOpen, Image, Video, FileAudio, File, Trash, Star, Clock, Tag, BarChart3, Users } from "lucide-react";
import { useAssets, useAssetStats } from "@/lib/store";

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export function Sidebar({ isOpen, setIsOpen }: SidebarProps) {
  const pathname = usePathname();
  const { total } = useAssets();
  const { stats: assetStats, loading: statsLoading, error: statsError } = useAssetStats();

  // Debug logging
  console.log('🔧 Sidebar: assetStats:', assetStats);
  console.log('🔧 Sidebar: statsLoading:', statsLoading);
  console.log('🔧 Sidebar: statsError:', statsError);
  console.log('🔧 Sidebar: total from useAssets:', total);

  // Use real asset counts from the database
  const assetCounts = {
    images: assetStats.images,
    videos: assetStats.videos,
    audio: assetStats.audio,
    documents: assetStats.documents,
    total: total || assetStats.total // Use total from useAssets if available, otherwise from stats
  };

  console.log('🔧 Sidebar: final assetCounts:', assetCounts);

  return (
    <aside 
      className={cn(
        "flex flex-col bg-sidebar-background border-r border-sidebar-border transition-all duration-300 overflow-y-auto",
        isOpen ? "w-56" : "w-16"
      )}
    >
      <div className="flex items-center justify-between h-14 px-4 border-b border-sidebar-border">
        <Link href="/" className="flex items-center space-x-2">
          <FolderOpen className="h-6 w-6 text-sidebar-primary" />
          {isOpen && <span className="font-semibold text-sidebar-primary">DAM System</span>}
        </Link>
        <button 
          onClick={() => setIsOpen(!isOpen)} 
          className="p-1 hover:bg-sidebar-accent rounded-md"
        >
          <ChevronRight className={cn(
            "h-4 w-4 text-sidebar-foreground transition-transform",
            isOpen ? "rotate-180" : ""
          )} />
        </button>
      </div>
      
      <div className="flex flex-col flex-1 pt-3">
        <nav>
          <h3 className={cn(
            "px-4 text-xs font-medium text-sidebar-foreground/60 mb-1",
            !isOpen && "sr-only"
          )}>
            Asset Types
          </h3>
          <ul className="space-y-1 px-2">
            <NavItem
              href="/assets/images"
              icon={<Image className="h-4 w-4" />}
              label="Images"
              count={statsLoading ? undefined : assetCounts.images}
              isActive={pathname === "/assets/images"}
              isExpanded={isOpen}
              loading={statsLoading}
            />
            <NavItem
              href="/assets/videos"
              icon={<Video className="h-4 w-4" />}
              label="Videos"
              count={statsLoading ? undefined : assetCounts.videos}
              isActive={pathname === "/assets/videos"}
              isExpanded={isOpen}
              loading={statsLoading}
            />
            <NavItem
              href="/assets/audio"
              icon={<FileAudio className="h-4 w-4" />}
              label="Audio"
              count={statsLoading ? undefined : assetCounts.audio}
              isActive={pathname === "/assets/audio"}
              isExpanded={isOpen}
              loading={statsLoading}
            />
            <NavItem
              href="/assets/documents"
              icon={<File className="h-4 w-4" />}
              label="Documents"
              count={statsLoading ? undefined : assetCounts.documents}
              isActive={pathname === "/assets/documents"}
              isExpanded={isOpen}
              loading={statsLoading}
            />
          </ul>
          
          <h3 className={cn(
            "px-4 text-xs font-medium text-sidebar-foreground/60 mt-6 mb-1",
            !isOpen && "sr-only"
          )}>
            Smart Filters
          </h3>
          <ul className="space-y-1 px-2">
            <NavItem 
              href="/starred" 
              icon={<Star className="h-4 w-4" />}
              label="Starred"
              isActive={pathname === "/starred"}
              isExpanded={isOpen}
            />
            <NavItem 
              href="/recent" 
              icon={<Clock className="h-4 w-4" />}
              label="Recent"
              isActive={pathname === "/recent"}
              isExpanded={isOpen}
            />
            <NavItem
              href="/stats"
              icon={<BarChart3 className="h-4 w-4" />}
              label="Statistics"
              isActive={pathname === "/stats"}
              isExpanded={isOpen}
            />
          </ul>

          <h3 className={cn(
            "px-4 text-xs font-medium text-sidebar-foreground/60 mt-6 mb-1",
            !isOpen && "sr-only"
          )}>
            Administration
          </h3>
          <ul className="space-y-1 px-2">
            <NavItem
              href="/admin/users"
              icon={<Users className="h-4 w-4" />}
              label="User Management"
              isActive={pathname === "/admin/users"}
              isExpanded={isOpen}
            />
          </ul>

        </nav>
      </div>
    </aside>
  );
}

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  count?: number;
  isActive: boolean;
  isExpanded: boolean;
  loading?: boolean;
}

function NavItem({ href, icon, label, count, isActive, isExpanded, loading }: NavItemProps) {
  return (
    <li>
      <Link
        href={href}
        className={cn(
          "flex items-center py-2 px-2 rounded-md group",
          isActive
            ? "bg-sidebar-accent text-sidebar-accent-foreground"
            : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
        )}
      >
        <span className="mr-2">{icon}</span>
        {isExpanded && (
          <>
            <span className="text-sm flex-1">{label}</span>
            {loading ? (
              <span className="text-xs text-sidebar-foreground/60 ml-auto">...</span>
            ) : count !== undefined ? (
              <span className="text-xs text-sidebar-foreground/60 ml-auto">
                {count.toLocaleString()}
              </span>
            ) : null}
          </>
        )}
      </Link>
    </li>
  );
}
