'use client'

import React, { useState, useEffect } from "react";
import { Menu, Upload, <PERSON>, <PERSON><PERSON>, User, LogOut } from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

interface HeaderProps {
  toggleSidebar: () => void;
}

export function Header({ toggleSidebar }: HeaderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
    router.push('/login');
  };

  return (
    <header className="border-b border-border bg-background px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button 
            onClick={toggleSidebar}
            className="mr-4 rounded-md p-2 hover:bg-secondary"
          >
            <Menu className="h-5 w-5" />
          </button>
          
          <nav className="hidden md:block">
            <ul className="flex space-x-6">
              <li>
                <Link 
                  href="/" 
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-primary",
                    pathname === "/" ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  Dashboard
                </Link>
              </li>
              <li>
                <Link 
                  href="/collections" 
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-primary",
                    pathname === "/collections" || pathname.startsWith("/collections/") 
                      ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  Collections
                </Link>
              </li>

            </ul>
          </nav>
        </div>

        <div className="flex items-center space-x-2">

          <Link
            href="/upload"
            className="inline-flex items-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground hover:bg-primary hover:bg-opacity-90"
          >
            <Upload className="mr-2 h-4 w-4" />
            <span className="hidden md:inline">Upload</span>
          </Link>



          <button className="rounded-md p-2 text-muted-foreground hover:bg-secondary">
            <Bell className="h-5 w-5" />
          </button>

          <Link
            href="/settings"
            className="rounded-md p-2 text-muted-foreground hover:bg-secondary"
          >
            <Settings className="h-5 w-5" />
          </Link>

          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="rounded-md p-2 text-muted-foreground hover:bg-secondary flex items-center gap-2"
            >
              <User className="h-5 w-5" />
              {user && <span className="hidden md:inline text-sm">{user.name}</span>}
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-background border rounded-md shadow-lg z-50">
                <div className="p-3 border-b">
                  <p className="text-sm font-medium">{user?.name}</p>
                  <p className="text-xs text-muted-foreground">{user?.email}</p>
                  <p className="text-xs text-muted-foreground capitalize">{user?.role}</p>
                </div>
                <div className="p-1">
                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-left hover:bg-secondary rounded-md"
                  >
                    <LogOut className="h-4 w-4" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
