'use client'

import React, { useState, useEffect, useRef } from "react";
import { Asset } from "@/lib/types";
import { motion } from "framer-motion";
import { File, FileVideo, FileAudio, FileText, Star, MoreHorizontal } from "lucide-react";

interface AssetCardProps {
  asset: Asset;
  onClick: () => void;
  onAssetUpdate?: (asset: Asset) => void;
}

export function AssetCard({ asset, onClick, onAssetUpdate }: AssetCardProps) {
  const [isHovering, setIsHovering] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isStarring, setIsStarring] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing once visible
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the element is visible
        threshold: 0.1
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const getAssetIcon = () => {
    switch (asset.type) {
      case 'video':
        return <FileVideo className="h-6 w-6" />;
      case 'audio':
        return <FileAudio className="h-6 w-6" />;
      case 'document':
        return <FileText className="h-6 w-6" />;
      default:
        return <File className="h-6 w-6" />;
    }
  };

  // Format date to a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get name from metadata or fallback to title
  const displayName = asset.metadata?.name || asset.title;

  // Extract folder name from file path
  const getFolderName = (filePath: string) => {
    if (!filePath) return '';

    // Split the path and get the parent directory
    const pathParts = filePath.split('/');
    if (pathParts.length <= 1) return '';

    // Get the immediate parent folder (last directory before the filename)
    const folderName = pathParts[pathParts.length - 2];

    // Clean up folder names for better display
    if (folderName.includes('folder')) {
      // For date folders like "01-01-2025 folder", extract just the date
      return folderName.replace(' folder', '');
    }

    return folderName;
  };

  const folderName = getFolderName(asset.filePath);

  const handleStarToggle = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the card click

    try {
      setIsStarring(true);
      const newStarredState = !asset.isStarred;

      const response = await fetch(`/api/assets/${asset.id}/star`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isStarred: newStarredState }),
      });

      if (!response.ok) {
        throw new Error('Failed to update star status');
      }

      const data = await response.json();
      if (data.success && onAssetUpdate) {
        onAssetUpdate(data.asset);
      }
    } catch (error) {
      console.error('Error toggling star:', error);
    } finally {
      setIsStarring(false);
    }
  };



  return (
    <div
      ref={cardRef}
      className="group relative rounded-md border border-border overflow-hidden bg-card"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div 
        className="aspect-square cursor-pointer overflow-hidden"
        onClick={onClick}
      >
        {asset.type === 'image' ? (
          isVisible ? (
            <img
              src={asset.thumbnailUrl}
              alt={displayName}
              className={`h-full w-full object-cover transition-all duration-300 group-hover:scale-105 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
              onLoad={() => setImageLoaded(true)}
              onError={(e) => {
                console.error('Failed to load thumbnail:', asset.thumbnailUrl);
                // Replace with fallback icon if image fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.parentElement!.innerHTML = '<div class="flex h-full w-full items-center justify-center bg-muted/30"><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" /></svg></div>';
              }}
              loading="lazy"
            />
          ) : (
            <div className="h-full w-full bg-muted/20 animate-pulse flex items-center justify-center">
              <div className="w-8 h-8 bg-muted/40 rounded"></div>
            </div>
          )
        ) : asset.type === 'video' ? (
          <div className="relative h-full w-full bg-black/20">
            {isVisible ? (
              <img
                src={asset.thumbnailUrl}
                alt={displayName}
                className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
            ) : (
              <div className="h-full w-full bg-muted/20 animate-pulse"></div>
            )}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="rounded-full bg-black bg-opacity-50 p-3">
                <FileVideo className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-muted/30">
            {getAssetIcon()}
          </div>
        )}
      </div>

      <div className="p-2">
        <h3 className="text-sm font-medium truncate">{displayName}</h3>
        {folderName && (
          <p className="text-xs text-blue-600 dark:text-blue-400 truncate">
            📁 {folderName}
          </p>
        )}
      </div>
      
      {isHovering && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute top-2 right-2 flex space-x-1"
        >
          <button
            onClick={handleStarToggle}
            disabled={isStarring}
            className={`rounded-full bg-background bg-opacity-70 p-1.5 hover:bg-opacity-90 transition-colors ${
              asset.isStarred
                ? 'text-yellow-500 hover:text-yellow-600'
                : 'text-muted-foreground hover:text-yellow-500'
            } ${isStarring ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={asset.isStarred ? 'Remove from starred' : 'Add to starred'}
          >
            <Star className={`h-4 w-4 ${asset.isStarred ? 'fill-current' : ''}`} />
          </button>
          <button className="rounded-full bg-background bg-opacity-70 p-1.5 hover:bg-opacity-90">
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </motion.div>
      )}
    </div>
  );
}
