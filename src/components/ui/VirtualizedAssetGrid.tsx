'use client'

import React, { useMemo, useCallback } from "react";
import { FixedSizeGrid as Grid } from "react-window";
import { Asset } from "@/lib/types";
import { AssetCard } from "./AssetCard";
import { AssetGridSkeleton } from "./SkeletonLoader";

interface VirtualizedAssetGridProps {
  assets: Asset[];
  onAssetClick: (asset: Asset) => void;
  loading?: boolean;
  showSkeleton?: boolean;
  containerHeight?: number;
  containerWidth?: number;
}

interface CellProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    assets: Asset[];
    onAssetClick: (asset: Asset) => void;
    columnsPerRow: number;
  };
}

const Cell = ({ columnIndex, rowIndex, style, data }: CellProps) => {
  const { assets, onAssetClick, columnsPerRow } = data;
  const index = rowIndex * columnsPerRow + columnIndex;
  const asset = assets[index];

  if (!asset) {
    return <div style={style} />;
  }

  return (
    <div style={{ ...style, padding: '8px' }}>
      <AssetCard asset={asset} onClick={() => onAssetClick(asset)} />
    </div>
  );
};

export function VirtualizedAssetGrid({
  assets,
  onAssetClick,
  loading = false,
  showSkeleton = false,
  containerHeight = 600,
  containerWidth = 1200
}: VirtualizedAssetGridProps) {
  // Show skeleton when explicitly requested or when loading with no assets
  if (showSkeleton || (loading && assets.length === 0)) {
    return <AssetGridSkeleton count={18} />;
  }

  if (assets.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No assets found</p>
      </div>
    );
  }

  // Calculate grid dimensions based on container width
  const itemWidth = 200; // Base width for each asset card
  const itemHeight = 280; // Height including card content
  const columnsPerRow = Math.floor(containerWidth / itemWidth);
  const rowCount = Math.ceil(assets.length / columnsPerRow);

  // Memoize the data object to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    assets,
    onAssetClick,
    columnsPerRow
  }), [assets, onAssetClick, columnsPerRow]);

  return (
    <div className="space-y-4">
      <Grid
        height={containerHeight}
        width={containerWidth}
        columnCount={columnsPerRow}
        columnWidth={itemWidth}
        rowCount={rowCount}
        rowHeight={itemHeight}
        itemData={itemData}
        overscanRowCount={2} // Render 2 extra rows for smooth scrolling
        overscanColumnCount={1}
      >
        {Cell}
      </Grid>
    </div>
  );
}

// Hook to get container dimensions
export function useContainerDimensions() {
  const [dimensions, setDimensions] = React.useState({
    width: 1200,
    height: 600
  });

  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    // Initial measurement
    updateDimensions();

    // Listen for resize events
    window.addEventListener('resize', updateDimensions);
    
    // Use ResizeObserver for more accurate container size tracking
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      window.removeEventListener('resize', updateDimensions);
      resizeObserver.disconnect();
    };
  }, []);

  return { dimensions, containerRef };
}
